import React from "react";
import { But<PERSON>, Grid, Input, Confirm } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import { useState } from "react";
import { useMediaQuery } from "react-responsive";
import { Link, useParams } from "react-router-dom";
import { openModal } from "../../../../app/common/modals/modalSlice";
import { searchFilter } from "../../../../app/common/util/util";
import TaskTransActiveWithDateList from "./TaskTransListWithDateList";
import TaskTransActiveWithoutDateList from "./TaskTransListWithoutDateList";
import { deleteTaskInDb } from "../../../../app/firestore/firestoreService";
import { toast } from "react-toastify";

export default function TaskTransActiveDashboard() {
  const dispatch = useDispatch();
  const { currentUserProfile } = useSelector((state) => state.profile);

  const {
    tasksTransWithDateUpcoming,
    tasksTransWithDatePast,
    tasksTransWithoutDateActive,
    tasksTransWithoutDateComplete,
  } = useSelector((state) => state.task);

  let { id } = useParams();
  const [searchTerms, setSearchTerms] = useState("");
  const [confirmDeleteAllOpen, setConfirmDeleteAllOpen] = useState(false);

  // Check if user is a manager
  const isManager = currentUserProfile?.authCustomClaims?.r === "m";

  const tasksWithDateUpcoming = searchFilter(
    tasksTransWithDateUpcoming?.filter(
      (oneTask) =>
        isManager ||
        !oneTask?.visibleTo ||
        oneTask?.visibleTo?.length === 0 ||
        oneTask?.visibleTo.includes(currentUserProfile.id)
    ),
    searchTerms
  );
  const tasksWithDatePast = searchFilter(
    tasksTransWithDatePast?.filter(
      (oneTask) =>
        isManager ||
        !oneTask?.visibleTo ||
        oneTask?.visibleTo?.length === 0 ||
        oneTask?.visibleTo.includes(currentUserProfile.id)
    ),
    searchTerms
  );
  const tasksWithoutDateActive = searchFilter(
    tasksTransWithoutDateActive?.filter(
      (oneTask) =>
        isManager ||
        !oneTask?.visibleTo ||
        oneTask?.visibleTo?.length === 0 ||
        oneTask?.visibleTo.includes(currentUserProfile.id)
    ),
    searchTerms
  );

  const tasksWithoutDateComplete = searchFilter(
    tasksTransWithoutDateComplete?.filter(
      (oneTask) =>
        isManager ||
        !oneTask?.visibleTo ||
        oneTask?.visibleTo?.length === 0 ||
        oneTask?.visibleTo.includes(currentUserProfile.id)
    ),
    searchTerms
  );
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  async function handleDeleteAllUpcomingDeadlines() {
    try {
      const deletePromises = tasksWithDateUpcoming.map((task) =>
        deleteTaskInDb(task.id)
      );
      await Promise.all(deletePromises);
      setConfirmDeleteAllOpen(false);
      toast.success(
        `Successfully deleted ${tasksWithDateUpcoming.length} upcoming deadline(s)`
      );
    } catch (error) {
      toast.error("Error deleting deadlines: " + error.message);
      setConfirmDeleteAllOpen(false);
    }
  }

  return (
    <div className="secondary-page-wrapper">
      <Grid stackable>
        <Grid.Column computer={5}>
          <Input
            type="text"
            fluid
            placeholder="Search by name or status"
            value={searchTerms}
            onChange={(e) => setSearchTerms(e.target.value)}
          ></Input>
        </Grid.Column>
        <Grid.Column computer={5}>
          <Button.Group fluid size="small">
            <Button active as={Link} to="">
              List
            </Button>
            <Button style={{ marginLeft: '2px' }} as={Link} to={`/transactions/${id}/tasksCalendar`}>
              Calendar
            </Button>
            <Button style={{ marginLeft: '2px' }} as={Link} to={`/transactions/${id}/tasksSharing`}>
              Sharing
            </Button>
          </Button.Group>
        </Grid.Column>
        <Grid.Column computer={6}>
          <Button
            color="blue"
            to="#"
            icon="plus"
            floated={isMobile ? "left" : "right"}
            size="small"
            className={isMobile ? "fluid mini bottom margin" : null}
            onClick={() =>
              dispatch(
                openModal({
                  modalType: "TaskForm",
                  modalProps: {
                    category: "Task",
                  },
                })
              )
            }
            content={`Add Task`}
            data-test="add-task-btn"
          />
          <Button
            color="blue"
            to="#"
            icon="plus"
            floated={isMobile ? "left" : "right"}
            size="small"
            className={isMobile ? "fluid" : null}
            onClick={() =>
              dispatch(
                openModal({
                  modalType: "TaskTemplatesSelect",
                })
              )
            }
            content={`Add Template`}
            data-test="add-task-btn"
          />
        </Grid.Column>
        <Grid.Column computer={8}>
          <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
            <h3>Upcoming Deadlines</h3>
            {tasksWithDateUpcoming?.length > 0 && (
              <Button
                color="red"
                size="tiny"
                onClick={() => setConfirmDeleteAllOpen(true)}
                content="Delete All"
              />
            )}
          </div>
          {tasksWithDateUpcoming?.length > 0 ? (
            <TaskTransActiveWithDateList tasks={tasksWithDateUpcoming} />
          ) : (
            <p className="tiny top margin">There are no upcoming deadlines for this transaction</p>
          )}
          {tasksWithDatePast?.length > 0 && (
            <>
              <h3>Past Deadlines</h3>
              <TaskTransActiveWithDateList tasks={tasksWithDatePast} />
            </>
          )}
        </Grid.Column>
        <Grid.Column computer={8}>
          <h3>To Do</h3>
          {tasksWithoutDateActive?.length > 0 ? (
            <TaskTransActiveWithoutDateList
              tasks={tasksWithoutDateActive}
              complete={false}
            />
          ) : (
            <p>There are no active tasks for this transaction</p>
          )}
          {tasksWithoutDateComplete?.length > 0 && (
            <>
              <h3>Complete</h3>
              <TaskTransActiveWithoutDateList
                tasks={tasksWithoutDateComplete}
                complete={true}
              />
            </>
          )}
        </Grid.Column>
      </Grid>
      <Confirm
        open={confirmDeleteAllOpen}
        content={`Are you sure you want to delete all ${tasksWithDateUpcoming?.length} upcoming deadline(s)? This action cannot be undone.`}
        onCancel={() => setConfirmDeleteAllOpen(false)}
        onConfirm={handleDeleteAllUpcomingDeadlines}
      />
    </div>
  );
}
