import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "semantic-ui-react";
import { toast } from "react-toastify";
import ClausesActionButtons from "../ClausesActionButtons";

export default function ClausesList({ clauses }) {
  const handleCopyToClipboard = async (text, title) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`"${title}" copied to clipboard`);
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement("textarea");
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand("copy");
      document.body.removeChild(textArea);
      toast.success(`"${title}" copied to clipboard`);
    }
  };

  return (
    <>
      {clauses?.length !== 0 &&
        clauses.map((clause) => (
          <Grid.Row key={clause.id} className="small top padding">
            <Divider />
            <Grid.Column width={3}>
              <b>{clause.title}</b>
            </Grid.Column>
            <Grid.Column width={11} style={{ whiteSpace: "pre-line" }}>
              {clause.text}
            </Grid.Column>
            <Grid.Column width={1}>
              <Button
                icon
                size="mini"
                onClick={() => handleCopyToClipboard(clause.text, clause.title)}
                title="Copy to clipboard"
              >
                <Icon name="copy" />
              </Button>
            </Grid.Column>
            <Grid.Column width={1}>
              <ClausesActionButtons clause={clause} />
            </Grid.Column>
          </Grid.Row>
        ))}
    </>
  );
}
