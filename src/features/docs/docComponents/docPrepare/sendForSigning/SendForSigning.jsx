import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Button,
  Divider,
  Form,
  Grid,
  Header,
  Icon,
  Input,
  Message,
  Popup,
  Segment,
  TextArea,
} from "semantic-ui-react";
import { useNavigate } from "react-router-dom";
import { useMediaQuery } from "react-responsive";
import { toast } from "react-toastify";
import {
  createSignerLists,
  roleIsOtherAgentsClients,
} from "./sendForSigningUtils";
import ModalWrapper from "../../../../../app/common/modals/modalWrapper";
import { createSignerColor } from "../annots/annotUtils";
import { closeModal } from "../../../../../app/common/modals/modalSlice";
import { uploadBlobToStorage } from "../../../../../app/firestore/firebaseService";
import { fillAndFlattenPdf } from "../../../../../app/pdfLib/pdfLib";
import {
  addHistoryToDb,
  sendDocSharingEmail,
  sendCustomDocSharingEmail,
  updateDocSentForSigningInDb,
} from "../../../../../app/firestore/firestoreService";
import { convertRoleToSharingDisplay } from "../../../../../app/common/util/util";

export default function SendForSigning() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { doc } = useSelector((state) => state.doc);
  const { transaction, transClients, parties, allParties } = useSelector(
    (state) => state.transaction
  );
  const { currentUserProfile } = useSelector((state) => state.profile);
  const [processingForms, setProcessingForms] = useState(false);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });
  const [signerListPossible, setSignerListPossible] = useState([]);
  const [signerListNotPossible, setSignerListNotPossible] = useState([]);
  const [signerListFinal, setSignerListFinal] = useState([]);
  const [note, setNote] = useState("");
  const [otherPartySignatureFields, setOtherPartySignatureFields] = useState(
    []
  );
  const [customEmailSubject, setCustomEmailSubject] = useState("");
  const [customEmailMessage, setCustomEmailMessage] = useState("");
  const [showCustomEmailFields, setShowCustomEmailFields] = useState(false);

  const thankYouSection = `
  Thank you,
${currentUserProfile?.firstName || ""} ${currentUserProfile?.lastName || ""}
${currentUserProfile?.email || ""}`;

  useEffect(() => {
    const newSignerLists = createSignerLists(doc, allParties, transaction);
    setSignerListPossible(newSignerLists.possible);
    setSignerListNotPossible(newSignerLists.notPossible);
    setSignerListFinal(newSignerLists.possible);

    // Check if custom email fields should be shown
    let shouldShowCustomFields = false;
    if (
      doc.canCustomizeEmailOnSendForSigs &&
      Array.isArray(doc.canCustomizeEmailOnSendForSigs)
    ) {
      // Check if any of the signers have roles that allow email customization
      newSignerLists.possible.forEach((signer) => {
        const roleDisplayName = convertRoleToSharingDisplay(signer.role);
        const roleLower = signer.role.toLowerCase();

        // Check for exact match or startsWith match for Buyer/Seller variants
        const hasExactMatch =
          doc.canCustomizeEmailOnSendForSigs.includes(roleDisplayName) ||
          doc.canCustomizeEmailOnSendForSigs.includes(signer.role) ||
          doc.canCustomizeEmailOnSendForSigs.includes(roleLower);

        const hasBuyerMatch =
          signer.role.startsWith("Buyer") &&
          doc.canCustomizeEmailOnSendForSigs.some((role) =>
            role.toLowerCase().startsWith("buyer")
          );

        const hasSellerMatch =
          signer.role.startsWith("Seller") &&
          doc.canCustomizeEmailOnSendForSigs.some((role) =>
            role.toLowerCase().startsWith("seller")
          );

        if (hasExactMatch || hasBuyerMatch || hasSellerMatch) {
          shouldShowCustomFields = true;
        }
      });
    }

    setShowCustomEmailFields(shouldShowCustomFields);

    // Set default email subject and message if custom fields should be shown
    if (shouldShowCustomFields) {
      // Find the appropriate role for defaults - prioritize the first signer
      let defaultRole = null;
      if (newSignerLists.possible.length > 0) {
        const firstSigner = newSignerLists.possible[0];
        if (firstSigner.role.startsWith("Buyer")) {
          defaultRole = "Buyer";
        } else if (firstSigner.role.startsWith("Seller")) {
          defaultRole = "Seller";
        } else {
          defaultRole = firstSigner.role;
        }
      }

      // Set default subject
      let defaultSubject =
        "Signature Needed" +
        (transaction?.address?.street
          ? " for " + transaction?.address?.street
          : ` from ${
              transaction.agentProfile?.firstName ||
              currentUserProfile?.firstName ||
              ""
            }`);
      if (
        doc.emailDefaultsSharing &&
        defaultRole &&
        doc.emailDefaultsSharing[defaultRole]?.subjectPlusAddress
      ) {
        defaultSubject =
          doc.emailDefaultsSharing[defaultRole].subjectPlusAddress +
          (transaction?.address?.street
            ? " for " + transaction?.address?.street
            : ` from ${
                transaction.agentProfile?.firstName ||
                currentUserProfile?.firstName ||
                ""
              }`);
      }
      setCustomEmailSubject(defaultSubject);

      // Set default message
      let defaultMessage = `Please sign the ${
        doc.name || "document"
      } that I've shared with you by clicking the "Sign Documents" button above. Let me know if you have any questions. ${thankYouSection},`;
      if (
        doc.emailDefaultsSharing &&
        defaultRole &&
        doc.emailDefaultsSharing[defaultRole]?.message
      ) {
        defaultMessage =
          doc.emailDefaultsSharing[defaultRole].message + `${thankYouSection}`;
      }
      setCustomEmailMessage(defaultMessage);
    }

    // Get other party signature fields
    const otherPartyFields = [];
    if (doc.annotsInProgress) {
      doc.annotsInProgress.forEach((annot) => {
        if (
          roleIsOtherAgentsClients(annot.signerRole, transaction) &&
          !annot.agentsField &&
          (annot.type === "signature" || annot.type === "initials")
        ) {
          // Find the party info for this signer role
          const partyInfo = allParties.find(
            (party) => party.role === annot.signerRole
          );
          const existingField = otherPartyFields.find(
            (field) => field.role === annot.signerRole
          );

          if (!existingField) {
            otherPartyFields.push({
              role: annot.signerRole,
              fullName: partyInfo
                ? `${partyInfo.firstName} ${partyInfo.lastName}`
                : annot.signerRole,
              hasName: partyInfo && partyInfo.firstName && partyInfo.lastName,
            });
          }
        }
      });
    }
    setOtherPartySignatureFields(otherPartyFields);
  }, [
    doc,
    transaction,
    parties,
    transClients,
    allParties,
    thankYouSection,
    currentUserProfile.firstName,
    dispatch,
  ]);

  function handleToggleSigner(party) {
    if (!signerListFinal?.includes(party)) {
      setSignerListFinal([...signerListFinal, party]);
    } else {
      setSignerListFinal(
        signerListFinal.filter((signer) => signer.role !== party.role)
      );
    }
  }

  function createSignerInfo(signer, includeEmail) {
    let signerInfo = signer.role;
    if (signer.firstName && signer.lastName) {
      signerInfo = signerInfo + ": " + signer.firstName + " " + signer.lastName;
    }
    if (signer.email && includeEmail) {
      signerInfo = signerInfo + " (" + signer.email + ")";
    }
    return signerInfo;
  }

  async function handleSendForSigning() {
    setProcessingForms(true);
    try {
      if (!doc.pdfBurnVersion) {
        const pdfBytes = await fillAndFlattenPdf(
          doc,
          transaction,
          currentUserProfile
        );
        await uploadBlobToStorage(doc.docRef, pdfBytes);
      }
      updateDocSentForSigningInDb(doc, note, signerListFinal, transaction);
      const agentProfile = transaction.agentProfile?.lastName
        ? transaction.agentProfile
        : currentUserProfile;

      // Send emails - use custom email if custom fields are shown, otherwise use standard email
      if (showCustomEmailFields && customEmailSubject && customEmailMessage) {
        await sendCustomDocSharingEmail(
          signerListFinal,
          "signing",
          agentProfile,
          transaction,
          customEmailSubject,
          customEmailMessage
        );
      } else {
        sendDocSharingEmail(
          signerListFinal,
          "signing",
          agentProfile,
          transaction
        );
      }
      signerListFinal &&
        signerListFinal.forEach((signer) => {
          if (signer.email) {
            addHistoryToDb(
              transaction.id,
              currentUserProfile,
              "sent for signing",
              doc.name,
              signer
            );
          }
        });
      setProcessingForms(false);
      toast.success("Document successfully sent for signing");
      navigate(`/transactions/${transaction.id}/documents`);
      dispatch(
        closeModal({
          modalType: "SendForSigning",
        })
      );
    } catch (error) {
      toast.error(error.message);
      setProcessingForms(false);
    }
  }

  function handleChange(e) {
    setNote(e.target.value);
  }

  function handleCancel() {
    dispatch(
      closeModal({
        modalType: "SendForSigning",
      })
    );
  }
  const modalWrapperSize = showCustomEmailFields ? "large" : "tiny";

  return (
    <ModalWrapper size={modalWrapperSize}>
      <Segment>
        <Grid>
          <Grid.Column width={16} className="zero bottom padding">
            <Header as="h2" color="blue" className="zero bottom margin">
              Who should we email for signatures?
            </Header>
            <Divider className="zero bottom margin" />
          </Grid.Column>
          <Grid.Column width={16}>
            {signerListPossible &&
            signerListPossible.length === 0 &&
            signerListNotPossible.length === 0 ? (
              <Grid.Column width={16}>
                <h3 className="text red tiny bottom margin">
                  Alert: There are no signable fields
                </h3>
                <p className="text red">
                  There are no signable fields added to this document so it
                  can't be sent out for signing. Please go back and drag
                  signable fields onto the document or share the document
                  instead of requesting signatures.
                </p>
              </Grid.Column>
            ) : (
              <p className="text-muted">
                Deselect anyone that you don't want to email about signing this
                form.
              </p>
            )}
            {signerListPossible?.map((signer) => (
              <Message
                key={signer.role}
                color={createSignerColor(signer.role, transaction)}
                onClick={() => handleToggleSigner(signer)}
                style={{ cursor: "pointer" }}
              >
                {signerListFinal &&
                signerListFinal.filter((e) => e.role === signer.role).length >
                  0 ? (
                  <Icon name="check square outline" size="large" />
                ) : (
                  <Icon name="square outline" size="large" />
                )}
                {createSignerInfo(signer, true)}
              </Message>
            ))}
            {showCustomEmailFields && (
              <>
                <Header as="h3" color="blue">
                  Email Subject:
                </Header>
                <Form>
                  <Input
                    fluid
                    value={customEmailSubject}
                    onChange={(e) => setCustomEmailSubject(e.target.value)}
                    placeholder="Email subject"
                  />
                </Form>

                <Header as="h3" color="blue" style={{ marginTop: "15px" }}>
                  Email Message:
                </Header>
                <Form>
                  <TextArea
                    rows={3}
                    value={customEmailMessage}
                    onChange={(e) => setCustomEmailMessage(e.target.value)}
                    placeholder="Email message"
                  />
                </Form>
              </>
            )}
            <Header
              as="h3"
              color="blue"
              style={{ marginTop: showCustomEmailFields ? "15px" : "0px" }}
            >
              Add a note (optional)
            </Header>
            The node will display on this document in their portal.
            <Popup
              flowing
              size="small"
              trigger={
                <Icon
                  name="info"
                  color="blue"
                  circular
                  inverted
                  size="tiny"
                  style={{
                    marginLeft: "3px",
                    marginBottom: "3px",
                    cursor: "pointer",
                  }}
                />
              }
            >
              <p>
                This note will display on the document in their portal. <br />
                The default message is 'Please sign this document.'
                <br />
                Example:
                <img
                  alt="Example of note on document"
                  className="ui large image"
                  src="https://storage.googleapis.com/transact-staging.appspot.com/public/screenshots/TA-PleaseSignThisDoc.png"
                />
              </p>
            </Popup>
            <Form>
              <TextArea
                rows={1}
                value={note}
                onChange={(e) => handleChange(e)}
              />
            </Form>
            {!isMobile && otherPartySignatureFields.length > 0 && (
              <>
                <Header as="h4" color="blue" style={{ marginTop: "20px" }}>
                  Additional Signature Fields Set Up
                </Header>
                <p style={{ color: "#666", fontSize: "12px" }}>
                  The following signature fields are set up for the other agent
                  to send for signatures to their clients:{" "}
                  <Popup
                    flowing
                    size="small"
                    trigger={
                      <Icon
                        name="info"
                        color="blue"
                        circular
                        inverted
                        size="tiny"
                        style={{
                          cursor: "help",
                          marginLeft: "3px",
                          marginBottom: "3px",
                        }}
                      />
                    }
                    position="top center"
                  >
                    <p className="bold text blue mini bottom margin">
                      Other Side's Clients
                    </p>
                    <p style={{ margin: "0 0 8px 0" }}>
                      <strong>Note:</strong> You do not share or send for
                      signing directly to the other side's clients.
                      <br />
                      When the other agent logs in (as a TransActioner
                      subscriber or not), <br />
                      they can easily send these documents out to their clients
                      for signatures.
                    </p>
                    <p className="bold text blue mini bottom margin">
                      Dual Agent
                    </p>
                    <p style={{ margin: "0" }}>
                      If you are acting as a dual agent, <br />
                      you should set up the other side as a separate
                      transaction, <br />
                      link the two transactions, <br />
                      and then send for signatures to the other side's clients
                      inside that other transaction.
                    </p>
                  </Popup>
                </p>
                {otherPartySignatureFields.map((field) => (
                  <div
                    key={field.role}
                    style={{
                      marginBottom: "3px",
                      paddingLeft: "10px",
                      marginTop: "3px",
                      fontSize: "12px",
                    }}
                  >
                    <strong>{field.role}:</strong>{" "}
                    {field.hasName ? field.fullName : "Name not provided"}
                  </div>
                ))}
              </>
            )}
          </Grid.Column>

          {signerListNotPossible && signerListNotPossible.length !== 0 && (
            <Grid.Column width={16}>
              <h3 className="text red tiny bottom margin">
                Alert: Missing Info
              </h3>
              <p className="text red">
                We can't email the following people because they are missing
                either a first name, last name, or email. If you want to email
                them go back to the 'Parties' page and add the missing info and
                then come back here.
              </p>
              {signerListNotPossible?.map((signer) => (
                <Message
                  key={signer.role}
                  color={createSignerColor(signer.role, transaction)}
                  style={{ cursor: "pointer" }}
                >
                  <Icon name="square outline" size="large" />
                  {createSignerInfo(signer, true)}
                </Message>
              ))}
            </Grid.Column>
          )}

          <Grid.Column width={16} className="zero top padding">
            <Divider className="zero top margin" />
            {signerListFinal &&
              signerListFinal.length !== 0 &&
              (processingForms ? (
                <Button
                  primary
                  loading
                  floated={isMobile ? null : "right"}
                  className={isMobile ? "fluid medium bottom margin" : null}
                >
                  Loading
                </Button>
              ) : (
                <Button
                  floated={isMobile ? null : "right"}
                  primary
                  onClick={() => handleSendForSigning()}
                  disabled={!doc || processingForms}
                  className={isMobile ? "fluid medium bottom margin" : null}
                >
                  Submit
                </Button>
              ))}

            <Button
              floated={isMobile ? null : "right"}
              onClick={() => handleCancel()}
              className={isMobile ? "fluid medium bottom margin" : null}
            >
              Cancel
            </Button>
          </Grid.Column>
        </Grid>
      </Segment>
    </ModalWrapper>
  );
}
